'use client'
import { useEffect, useRef, useState } from 'react'
import { useQuery, useLazyQuery } from '@apollo/client'
import { GET_LIVETV, GET_STREAM_URL } from '@/lib/queries'
import Hls from 'hls.js'

export default function LiveTVInterface() {
  const [channels, setChannels] = useState([])
  const [currentChannelIndex, setCurrentChannelIndex] = useState(-1)
  const [favorites, setFavorites] = useState([])
  const [isChannelSelectorVisible, setIsChannelSelectorVisible] = useState(false)
  const [isFavoritesVisible, setIsFavoritesVisible] = useState(false)
  const [isActivated, setIsActivated] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [currentStreamUrl, setCurrentStreamUrl] = useState('')

  const playerVideoRef = useRef(null)
  const playerIframeRef = useRef(null)
  const hlsInstanceRef = useRef(null)

  const { data: liveTVData, loading } = useQuery(GET_LIVETV, {
    variables: { limit: 100, page: 1 }
  })

  const [getStreamUrl] = useLazyQuery(GET_STREAM_URL)

  useEffect(() => {
    if (liveTVData?.liveTV) {
      setChannels(liveTVData.liveTV)
      if (liveTVData.liveTV.length > 0) {
        setCurrentChannelIndex(0)
      }
    }
  }, [liveTVData])

  // Cleanup HLS instance on unmount
  useEffect(() => {
    return () => {
      if (hlsInstanceRef.current) {
        hlsInstanceRef.current.destroy()
        hlsInstanceRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    // Load favorites from localStorage
    const savedFavorites = localStorage.getItem('livetv-favorites')
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites))
      } catch (e) {
        console.error('Error loading favorites:', e)
      }
    }
  }, [])

  useEffect(() => {
    // Keyboard navigation
    const handleKeyDown = (e) => {
      if (!isActivated) {
        if (e.key === 'ArrowRight') {
          e.preventDefault()
          setIsActivated(true)
          setIsChannelSelectorVisible(true)
        }
        return
      }

      if (isFavoritesVisible) {
        // Handle favorites navigation
        if (e.key === 'ArrowLeft' || e.key === 'Escape') {
          e.preventDefault()
          setIsFavoritesVisible(false)
        }
        return
      }

      // Main channel navigation
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault()
          previousChannel()
          break
        case 'ArrowDown':
          e.preventDefault()
          nextChannel()
          break
        case 'ArrowRight':
          e.preventDefault()
          setIsFavoritesVisible(true)
          break
        case 'Enter':
          e.preventDefault()
          toggleFavorite()
          break
        case 'Escape':
          e.preventDefault()
          setIsActivated(false)
          setIsChannelSelectorVisible(false)
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isActivated, isFavoritesVisible, currentChannelIndex, channels])

  const previousChannel = () => {
    if (channels.length === 0) return
    const newIndex = currentChannelIndex > 0 ? currentChannelIndex - 1 : channels.length - 1
    setCurrentChannelIndex(newIndex)
    playChannel(channels[newIndex])
  }

  const nextChannel = () => {
    if (channels.length === 0) return
    const newIndex = currentChannelIndex < channels.length - 1 ? currentChannelIndex + 1 : 0
    setCurrentChannelIndex(newIndex)
    playChannel(channels[newIndex])
  }

  const playChannel = async (channel) => {
    if (!channel || !channel.streamingUrls || channel.streamingUrls.length === 0) {
      setError('No streaming URLs available for this channel')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      console.log('Playing channel:', channel.title)

      // Clean up previous HLS instance
      if (hlsInstanceRef.current) {
        hlsInstanceRef.current.destroy()
        hlsInstanceRef.current = null
      }

      // Get the first available streaming URL
      const streamingUrl = channel.streamingUrls[0]
      console.log('Using streaming URL:', streamingUrl)

      // Try to get the source stream URL using GraphQL resolver
      try {
        const { data: streamData } = await getStreamUrl({
          variables: {
            itemId: channel.id,
            type: 'LIVETV',
            streamId: streamingUrl.id
          }
        })

        if (streamData?.stream?.sourceStreamUrl) {
          console.log('Got source stream URL:', streamData.stream.sourceStreamUrl)
          await loadStream(streamData.stream.sourceStreamUrl, streamData.stream.method || 'hls')
        } else {
          // Fallback to original URL
          console.log('No source stream URL, using original:', streamingUrl.url)
          await loadStream(streamingUrl.url, 'hls')
        }
      } catch (streamError) {
        console.error('Error getting stream URL:', streamError)
        // Fallback to original URL
        console.log('Fallback to original URL:', streamingUrl.url)
        await loadStream(streamingUrl.url, 'hls')
      }

    } catch (error) {
      console.error('Error playing channel:', error)
      setError(`Failed to play channel: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const getProxyUrl = async (originalUrl) => {
    // Check if this is a WiTV URL that needs proxy authentication
    if (originalUrl.includes('witv') || originalUrl.includes('play.witv')) {
      try {
        console.log('Getting proxy URL for WiTV stream:', originalUrl)
        const response = await fetch(`/api/proxy-token?url=${encodeURIComponent(originalUrl)}`)

        if (response.ok) {
          const proxyUrl = await response.text()
          console.log('Got proxy URL:', proxyUrl)
          return proxyUrl
        } else {
          console.error('Proxy token request failed:', response.status, response.statusText)
          return originalUrl // Fallback to original URL
        }
      } catch (error) {
        console.error('Error getting proxy URL:', error)
        return originalUrl // Fallback to original URL
      }
    }

    return originalUrl // Return original URL for non-WiTV streams
  }

  const loadStream = async (url, method = 'hls') => {
    if (!url) {
      throw new Error('No stream URL provided')
    }

    // Get proxy URL if needed
    const finalUrl = await getProxyUrl(url)
    setCurrentStreamUrl(finalUrl)

    if (method === 'iframe') {
      // Hide video player, show iframe
      if (playerVideoRef.current) {
        playerVideoRef.current.style.display = 'none'
      }
      if (playerIframeRef.current) {
        playerIframeRef.current.style.display = 'block'
        playerIframeRef.current.src = finalUrl
      }
    } else {
      // Hide iframe, show video player
      if (playerIframeRef.current) {
        playerIframeRef.current.style.display = 'none'
        playerIframeRef.current.src = ''
      }
      if (playerVideoRef.current) {
        playerVideoRef.current.style.display = 'block'
      }

      // Handle HLS streams
      if (finalUrl.includes('.m3u8') || method === 'hls') {
        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: true,
            lowLatencyMode: true,
            backBufferLength: 90,
            xhrSetup: function (xhr, url) {
              // Add headers for CORS and authentication
              xhr.setRequestHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            }
          })

          hlsInstanceRef.current = hls

          hls.loadSource(finalUrl)
          hls.attachMedia(playerVideoRef.current)

          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            console.log('HLS manifest parsed, starting playback')
            playerVideoRef.current.play().catch(e => {
              console.error('Error starting playback:', e)
              setError('Failed to start playback. Please try again.')
            })
          })

          hls.on(Hls.Events.ERROR, (event, data) => {
            console.error('HLS error:', data)
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  console.log('Network error, trying to recover...')
                  hls.startLoad()
                  break
                case Hls.ErrorTypes.MEDIA_ERROR:
                  console.log('Media error, trying to recover...')
                  hls.recoverMediaError()
                  break
                default:
                  console.log('Fatal error, destroying HLS instance')
                  hls.destroy()
                  setError('Stream error occurred. Please try another channel.')
                  break
              }
            }
          })

        } else if (playerVideoRef.current.canPlayType('application/vnd.apple.mpegurl')) {
          // Native HLS support (Safari)
          playerVideoRef.current.src = finalUrl
          playerVideoRef.current.play().catch(e => {
            console.error('Error starting native HLS playback:', e)
            setError('Failed to start playback. Please try again.')
          })
        } else {
          setError('HLS not supported in this browser')
        }
      } else {
        // Direct MP4 or other video formats
        playerVideoRef.current.src = finalUrl
        playerVideoRef.current.play().catch(e => {
          console.error('Error starting direct playback:', e)
          setError('Failed to start playback. Please try again.')
        })
      }
    }
  }

  const toggleFavorite = () => {
    if (currentChannelIndex < 0 || !channels[currentChannelIndex]) return
    
    const channel = channels[currentChannelIndex]
    const isFavorite = favorites.includes(channel.id)
    
    let newFavorites
    if (isFavorite) {
      newFavorites = favorites.filter(id => id !== channel.id)
    } else {
      newFavorites = [...favorites, channel.id]
    }
    
    setFavorites(newFavorites)
    localStorage.setItem('livetv-favorites', JSON.stringify(newFavorites))
  }

  const getChannelCards = () => {
    if (channels.length === 0) return []
    
    const cards = []
    for (let i = -2; i <= 2; i++) {
      let index = currentChannelIndex + i
      if (index < 0) index = channels.length + index
      if (index >= channels.length) index = index - channels.length
      
      cards.push({
        position: i === -2 ? 'prev-2' : i === -1 ? 'prev-1' : i === 0 ? 'current' : i === 1 ? 'next-1' : 'next-2',
        channel: channels[index],
        isFavorite: favorites.includes(channels[index]?.id)
      })
    }
    
    return cards
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">
          <i className="fas fa-spinner fa-spin mr-3"></i>
          Loading Live TV...
        </div>
      </div>
    )
  }

  return (
    <section id="livetv" className="min-h-screen bg-black relative" tabIndex="0">
      {/* Sidebar Hover Trigger */}
      <div className="absolute left-0 top-0 w-4 h-full z-10" id="livetv-sidebar-trigger"></div>

      {/* Activation Hint */}
      {!isActivated && (
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="bg-black bg-opacity-75 text-white px-6 py-4 rounded-lg flex items-center">
            <i className="fas fa-arrow-right mr-3"></i>
            <span>Press → or click to access channels</span>
          </div>
        </div>
      )}

      {/* Full Screen TV Player */}
      <div className="absolute inset-0">
        <div className="absolute top-4 left-4 text-white text-2xl font-bold z-10">
          NetStream Live TV
          {currentChannelIndex >= 0 && channels[currentChannelIndex] && (
            <div className="text-lg font-normal mt-1">
              {channels[currentChannelIndex].title}
            </div>
          )}
        </div>

        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-20">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
              <p className="text-xl">Loading stream...</p>
            </div>
          </div>
        )}

        {/* Error Overlay */}
        {error && (
          <div className="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center z-20">
            <div className="text-white text-center max-w-md">
              <i className="fas fa-exclamation-triangle text-6xl mb-4 text-red-500"></i>
              <h2 className="text-2xl font-bold mb-2">Stream Error</h2>
              <p className="mb-4">{error}</p>
              <button
                onClick={() => setError(null)}
                className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded"
              >
                Dismiss
              </button>
            </div>
          </div>
        )}

        <video
          ref={playerVideoRef}
          id="livetv-player-video"
          className="w-full h-full object-cover"
          playsInline
          controls={false}
        />

        <iframe
          ref={playerIframeRef}
          id="livetv-player-iframe"
          className="w-full h-full hidden"
          allowFullScreen
        />

        {/* Simple TV Controls */}
        <div className="absolute bottom-4 left-4 flex space-x-2 z-10">
          <button className="bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75 transition-all">
            <i className="fas fa-play"></i>
          </button>
          <button className="bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75 transition-all">
            <i className="fas fa-expand"></i>
          </button>
        </div>

        {/* Channel Selector Overlay */}
        {isChannelSelectorVisible && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20">
            <div className="bg-black bg-opacity-75 rounded-lg p-4 min-w-64">
              <div className="flex items-center mb-4">
                <i className="fas fa-star text-yellow-500 mr-2"></i>
                <i className="fas fa-arrow-right text-white"></i>
              </div>

              {/* Rolling Channel Cards */}
              <div className="space-y-2">
                {getChannelCards().map((card, index) => (
                  <div 
                    key={index}
                    className={`p-3 rounded transition-all ${
                      card.position === 'current' 
                        ? 'bg-blue-600 text-white scale-105' 
                        : 'bg-gray-700 text-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold">
                          {card.channel?.title || 'Channel'}
                        </div>
                        {card.position === 'current' && (
                          <div className="text-sm opacity-75">
                            {card.channel?.metadata?.synopsis || 'Live TV'}
                          </div>
                        )}
                      </div>
                      <i className={`fas fa-star ${card.isFavorite ? 'text-yellow-500' : 'text-gray-500'}`}></i>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Favorites List */}
        {isFavoritesVisible && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-30">
            <div className="bg-black bg-opacity-90 rounded-lg p-4 min-w-64">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-bold">Favorites</h3>
                <button 
                  onClick={() => setIsFavoritesVisible(false)}
                  className="text-white hover:text-gray-300"
                >
                  <i className="fas fa-arrow-left"></i>
                </button>
              </div>

              <div className="space-y-2">
                {favorites.length === 0 ? (
                  <div className="text-gray-400 text-center py-4">
                    No favorites yet
                  </div>
                ) : (
                  favorites.map(favoriteId => {
                    const channel = channels.find(c => c.id === favoriteId)
                    return channel ? (
                      <div 
                        key={favoriteId}
                        className="p-3 bg-gray-700 rounded text-white hover:bg-gray-600 cursor-pointer transition-all"
                        onClick={() => {
                          const index = channels.findIndex(c => c.id === favoriteId)
                          if (index >= 0) {
                            setCurrentChannelIndex(index)
                            playChannel(channel)
                            setIsFavoritesVisible(false)
                          }
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <span>{channel.title}</span>
                          <i className="fas fa-star text-yellow-500"></i>
                        </div>
                      </div>
                    ) : null
                  })
                )}
              </div>
            </div>
          </div>
        )}

        {/* Show Channel Selector Button */}
        <button 
          className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-75 transition-all z-10"
          onClick={() => {
            setIsActivated(true)
            setIsChannelSelectorVisible(!isChannelSelectorVisible)
          }}
        >
          <i className="fas fa-list"></i>
        </button>
      </div>
    </section>
  )
}

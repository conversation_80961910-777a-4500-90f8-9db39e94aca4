import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../lib/mongodb';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request) {
  try {
    const body = await request.json();
    const { 
      mode = 'latest', 
      type = 'all', 
      pages = { movies: 2, series: 2, anime: 2, livetv: 4 }, 
      enrichment = false, 
      gemini = false 
    } = body;

    // Connect to database to log the scraping job
    const db = await connectToDatabase();
    
    // Create a scraping job record
    const jobId = `scrape_${Date.now()}`;
    const scrapingJob = {
      jobId,
      mode,
      type,
      pages,
      enrichment,
      gemini,
      status: 'started',
      startedAt: new Date(),
      estimatedDuration: `${Object.values(pages).reduce((sum, pageCount) => sum + pageCount, 0) * 2} minutes`
    };

    // Save job to database
    await db.collection('scraping_jobs').insertOne(scrapingJob);

    // Start the actual scraping process in the background
    // This will trigger the scraping service from the main project
    process.nextTick(async () => {
      try {
        console.log(`Starting scraping job ${jobId} with mode: ${mode}, type: ${type}, pages:`, pages);
        
        // Update job status to running
        await db.collection('scraping_jobs').updateOne(
          { jobId },
          { 
            $set: { 
              status: 'running',
              startedAt: new Date()
            }
          }
        );

        // Execute the standalone scraping script from the main project
        const scriptPath = '../src/scripts/standaloneScrape.js';
        const envVars = {
          SCRAPE_MODE: mode,
          SCRAPE_TYPE: type,
          SCRAPE_PAGES_MOVIES: pages.movies?.toString() || '2',
          SCRAPE_PAGES_SERIES: pages.series?.toString() || '2',
          SCRAPE_PAGES_ANIME: pages.anime?.toString() || '2',
          SCRAPE_PAGES_LIVETV: pages.livetv?.toString() || '4',
          ENABLE_ENRICHMENT: enrichment.toString(),
          ENABLE_GEMINI: gemini.toString(),
          JOB_ID: jobId
        };

        // Build environment variables string
        const envString = Object.entries(envVars)
          .map(([key, value]) => `${key}=${value}`)
          .join(' ');

        // Execute the standalone scraping script from the parent directory (main project)
        const { stdout, stderr } = await execAsync(`${envString} node ${scriptPath}`, {
          cwd: process.cwd() + '/..', // Run from parent directory (main project root)
          maxBuffer: 1024 * 1024 * 10 // 10MB buffer
        });

        console.log(`Scraping job ${jobId} completed successfully`);
        console.log('STDOUT:', stdout);
        if (stderr) console.log('STDERR:', stderr);

        // Update job status to completed
        await db.collection('scraping_jobs').updateOne(
          { jobId },
          { 
            $set: { 
              status: 'completed',
              completedAt: new Date(),
              output: stdout,
              error: stderr || null
            }
          }
        );

      } catch (error) {
        console.error(`Scraping job ${jobId} failed:`, error);
        
        // Update job status to failed
        await db.collection('scraping_jobs').updateOne(
          { jobId },
          { 
            $set: { 
              status: 'failed',
              failedAt: new Date(),
              error: error.message
            }
          }
        );
      }
    });

    return NextResponse.json({
      success: true,
      message: `Scraping job started successfully`,
      data: {
        ...scrapingJob,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Scraping API error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to start scraping: ${error.message}`
    }, { status: 500 });
  }
} 
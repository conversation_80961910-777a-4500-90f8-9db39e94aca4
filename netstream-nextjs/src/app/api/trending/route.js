import { NextResponse } from 'next/server'

// Extract the base URL from API_URL (server-side) or NEXT_PUBLIC_API_URL (client-side) or use default
const GRAPHQL_ENDPOINT = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql'

// GraphQL queries for trending content using the proper TRENDING sort
const TRENDING_QUERIES = {
  movies: `
    query GetTrendingMovies($limit: Int, $page: Int) {
      movies(limit: $limit, page: $page, sort: TRENDING) {
        id
        title
        displayTitle
        thumbnail
        image
        trendingRank
        trendingRankDisplay
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  series: `
    query GetTrendingSeries($limit: Int, $page: Int) {
      series(limit: $limit, page: $page, sort: TRENDING) {
        id
        title
        displayTitle
        thumbnail
        image
        season
        trendingRank
        trendingRankDisplay
        episodes {
          episodeNumber
          streamingUrls {
            language
          }
        }
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  anime: `
    query GetTrendingAnime($limit: Int, $page: Int) {
      anime(limit: $limit, page: $page, sort: TRENDING) {
        id
        title
        displayTitle
        thumbnail
        image
        trendingRank
        trendingRankDisplay
        episodes {
          episodeNumber
        }
        metadata {
          year
          synopsis
        }
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          synopsis
          images {
            jpg {
              image_url
              small_image_url
              large_image_url
            }
          }
          score
          status
        }
      }
    }
  `
}

export async function GET() {
  try {
    console.log('🔄 Trending API: Fetching mixed trending content from trendingitems...')

    const mixedTrending = []
    const itemsPerType = 7 // Get 7 items from each type for a total of ~21 items

    // Fetch movies, series, and anime trending content in parallel
    const promises = Object.entries(TRENDING_QUERIES).map(async ([type, query]) => {
      try {
        console.log(`📡 Trending API: Fetching trending ${type}...`)

        const response = await fetch(GRAPHQL_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query,
            variables: {
              limit: itemsPerType,
              page: 1
            }
          })
        })

        if (!response.ok) {
          throw new Error(`GraphQL request failed for ${type}: ${response.status}`)
        }

        const result = await response.json()

        if (result.errors) {
          console.error(`❌ Trending API: GraphQL errors for ${type}:`, result.errors)
          return []
        }

        // Extract data based on type
        let items = []
        if (type === 'movies') {
          items = result.data?.movies || []
        } else if (type === 'series') {
          items = result.data?.series || []
        } else if (type === 'anime') {
          items = result.data?.anime || []
        }

        // Add type information to each item
        return items.map(item => ({
          ...item,
          __typename: type === 'movies' ? 'Movie' : type === 'series' ? 'Series' : 'Anime',
          type: type
        }))

      } catch (error) {
        console.error(`💥 Trending API error for ${type}:`, error)
        return []
      }
    })

    const results = await Promise.all(promises)

    // Flatten and mix the results
    results.forEach(typeItems => {
      mixedTrending.push(...typeItems)
    })

    // Shuffle the mixed trending for variety
    for (let i = mixedTrending.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [mixedTrending[i], mixedTrending[j]] = [mixedTrending[j], mixedTrending[i]]
    }

    console.log(`✅ Trending API: Returning ${mixedTrending.length} mixed trending items`)

    return NextResponse.json({
      success: true,
      data: mixedTrending
    })

  } catch (error) {
    console.error('💥 Trending API error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}

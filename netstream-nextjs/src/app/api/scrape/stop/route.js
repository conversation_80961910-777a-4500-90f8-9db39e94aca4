import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST() {
  try {
    const db = await connectToDatabase();
    
    // Find the currently running scraping job
    const runningJob = await db.collection('scraping_jobs')
      .findOne({ status: 'running' }, { sort: { startedAt: -1 } });

    if (!runningJob) {
      return NextResponse.json({
        success: false,
        error: 'No running scraping job found'
      }, { status: 404 });
    }

    console.log(`Stopping scraping job: ${runningJob.jobId}`);

    // Update job status to stopped
    await db.collection('scraping_jobs').updateOne(
      { jobId: runningJob.jobId },
      { 
        $set: { 
          status: 'stopped',
          stoppedAt: new Date(),
          stoppedBy: 'admin'
        }
      }
    );

    // Try to kill the scraping process if it's still running
    try {
      // Kill any Node.js processes that might be running the scraping script
      // This is a basic approach - in production you might want more sophisticated process management
      const { stdout, stderr } = await execAsync('pkill -f "scrapeWorker.js"', {
        cwd: process.cwd()
      });
      
      if (stdout) console.log('Process kill output:', stdout);
      if (stderr) console.log('Process kill stderr:', stderr);
      
    } catch (killError) {
      console.warn('Could not kill scraping process:', killError.message);
      // This is not critical - the job is already marked as stopped in the database
    }

    const stopData = {
      jobId: runningJob.jobId,
      status: 'stopped',
      stoppedAt: new Date().toISOString(),
      message: 'Scraping job stopped by admin'
    };

    console.log(`Scraping job ${runningJob.jobId} stopped successfully`);

    return NextResponse.json({
      success: true,
      message: 'Scraping job stopped successfully',
      data: stopData
    });

  } catch (error) {
    console.error('Stop scraping error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to stop scraping: ${error.message}`
    }, { status: 500 });
  }
} 
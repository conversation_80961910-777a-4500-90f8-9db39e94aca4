import { gql } from '@apollo/client'

// Media Queries - Fixed to match actual GraphQL schema
export const GET_MOVIES = gql`
  query GetMovies($limit: Int, $page: Int, $sort: SortOption) {
    movies(limit: $limit, page: $page, sort: $sort) {
      id
      title
      displayTitle
      thumbnail
      image
      trendingRank
      trendingRankDisplay
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

export const GET_SERIES = gql`
  query GetSeries($limit: Int, $page: Int, $sort: SortOption) {
    series(limit: $limit, page: $page, sort: $sort) {
      id
      title
      displayTitle
      detailUrl
      detailUrlPath
      cleanedTitle
      thumbnail
      image
      season
      metadata {
        synopsis
        actors
        year
        genre
        origin
        creator
        duration
      }
      tmdb {
        id
        title
        original_title
        overview
        poster_path
        backdrop_path
        release_date
        vote_average
        vote_count
        genres
        number_of_seasons
        number_of_episodes
        in_production
        status
      }
    }
  }
`

export const GET_ANIME = gql`
  query GetAnime($limit: Int, $page: Int, $sort: SortOption) {
    anime(limit: $limit, page: $page, sort: $sort) {
      id
      title
      displayTitle
      detailUrl
      detailUrlPath
      cleanedTitle
      thumbnail
      image
      season
      metadata {
        synopsis
        actors
        year
        genre
        origin
        creator
        duration
      }
      jikan {
        mal_id
        title {
          default
          english
          japanese
        }
        type
        source
        episodes
        status
        airing
        aired {
          from
          to
          string
        }
        duration
        rating
        score
        scored_by
        rank
        popularity
        members
        favorites
        synopsis
        background
        season
        year
        studios {
          id
          name
        }
        genres {
          id
          name
        }
        themes {
          id
          name
        }
        demographics {
          id
          name
        }
        images {
          jpg {
            image_url
            small_image_url
            large_image_url
          }
          webp {
            image_url
            small_image_url
            large_image_url
          }
        }
      }
    }
  }
`

export const GET_LIVETV = gql`
  query GetLiveTV($limit: Int, $page: Int) {
    liveTV(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      streamingUrls {
        id
        url
        provider
        language
        lastChecked
        isActive
        sourceStreamUrl
        size
        type
        method
      }
      metadata {
        year
        synopsis
      }
    }
  }
`

export const GET_MEDIA_DETAIL = gql`
  query GetMediaDetail($id: ID!, $type: ItemType!) {
    item(id: $id, type: $type) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
      ... on Anime {
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          synopsis
          images {
            jpg {
              image_url
              small_image_url
              large_image_url
            }
          }
          score
          scored_by
          status
          aired {
            from
            to
          }
          episodes
          duration
          rating
          genres {
            mal_id
            name
          }
        }
        episodes {
          episodeNumber
          season
          language
          streamingUrls {
            id
            url
            provider
            language
          }
        }
        tmdbSeasons {
          air_date
          tmdb_season_id
          name
          overview
          poster_path
          season_number
          vote_average
          episodes {
            air_date
            episode_number
            tmdb_episode_id
            name
            overview
            still_path
            vote_average
          }
        }
      }
      ... on Series {
        episodes {
          episodeNumber
          season
          language
          streamingUrls {
            id
            url
            provider
            language
          }
        }
        tmdbSeasons {
          air_date
          tmdb_season_id
          name
          overview
          poster_path
          season_number
          vote_average
          episodes {
            air_date
            episode_number
            tmdb_episode_id
            name
            overview
            still_path
            vote_average
          }
        }
      }
      ... on Movie {
        streamingUrls {
          id
          url
          provider
          language
        }
      }
      ... on LiveTV {
        streamingUrls {
          id
          url
          provider
          language
        }
      }
    }
  }
`

export const GET_LIVETV_DETAIL = gql`
  query GetLiveTVDetail($id: ID!) {
    liveTVDetail(id: $id) {
      id
      name
      logo
      group
      url
      country
      language
      sources {
        id
        url
        quality
      }
    }
  }
`

// Latest content queries
export const GET_LATEST_MOVIES = gql`
  query GetLatestMovies($limit: Int, $page: Int, $excludeAncien: Boolean) {
    latestMovies(limit: $limit, page: $page, excludeAncien: $excludeAncien) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

export const GET_ANCIEN_MOVIES = gql`
  query GetAncienMovies($limit: Int, $page: Int) {
    ancienMovies(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

export const GET_LATEST_SERIES = gql`
  query GetLatestSeries($limit: Int, $page: Int) {
    latestSeries(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      tmdb {
        id
        title
        overview
        poster_path
        release_date
        vote_average
        vote_count
        genres
      }
    }
  }
`

export const GET_LATEST_ANIME = gql`
  query GetLatestAnime($limit: Int, $page: Int) {
    latestAnime(limit: $limit, page: $page) {
      id
      title
      displayTitle
      thumbnail
      image
      metadata {
        year
        synopsis
      }
      jikan {
        mal_id
        title {
          default
          english
          japanese
        }
        synopsis
        images {
          jpg {
            image_url
            small_image_url
            large_image_url
          }
        }
        score
        status
      }
    }
  }
`

// Genre queries removed - now handled directly in GenreCarousels component

export const SEARCH_MEDIA = gql`
  query SearchMedia($query: String!, $page: Int, $limit: Int) {
    search(query: $query, page: $page, limit: $limit) {
      items {
        ... on Movie {
          __typename
          id
          title
          displayTitle
          detailUrl
          detailUrlPath
          cleanedTitle
          thumbnail
          image
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          streamingUrls {
            id
            url
            provider
            language
            lastChecked
            isActive
            sourceStreamUrl
            size
            type
            method
          }
          tmdb {
            id
            title
            original_title
            poster_path
            backdrop_path
            overview
            release_date
            vote_average
            vote_count
            genres
          }
        }
        ... on Series {
          __typename
          id
          title
          displayTitle
          detailUrl
          detailUrlPath
          cleanedTitle
          thumbnail
          image
          season
          episodes {
            episodeNumber
            season
            language
            streamingUrls {
              id
              url
              provider
              language
              lastChecked
              isActive
              sourceStreamUrl
              size
              type
              method
            }
          }
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          tmdb {
            id
            title
            original_title
            poster_path
            backdrop_path
            overview
            release_date
            vote_average
            vote_count
            genres
            number_of_seasons
            number_of_episodes
            in_production
            status
          }
        }
        ... on Anime {
          __typename
          id
          title
          displayTitle
          detailUrl
          detailUrlPath
          thumbnail
          image
          season
          animeLanguage
          episodes {
            episodeNumber
            season
            language
            streamingUrls {
              id
              url
              provider
              language
              lastChecked
              isActive
              sourceStreamUrl
              size
              type
              method
            }
          }
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          jikan {
            mal_id
            title {
              default
              english
              japanese
            }
            type
            source
            episodes
            status
            airing
            aired {
              from
              to
              string
            }
            duration
            rating
            score
            scored_by
            rank
            popularity
            members
            favorites
            synopsis
            background
            season
            year
            studios {
              mal_id
              name
            }
            genres {
              mal_id
              name
              type
            }
            themes {
              mal_id
              name
              type
            }
            demographics {
              mal_id
              name
              type
            }
            images {
              jpg {
                image_url
                small_image_url
                large_image_url
              }
              webp {
                image_url
                small_image_url
                large_image_url
              }
            }
            trailer {
              youtube_id
              url
              embed_url
            }
            approved
            relations {
              relation
              entry {
                mal_id
                type
                name
                url
              }
            }
            streaming_platforms {
              name
              url
            }
            lastUpdated
          }
        }
      }
    }
  }
`

// Stream and Play queries for video playback
export const GET_STREAM_URL = gql`
  query GetStreamUrl($itemId: ID!, $type: ItemType!, $streamId: ID!) {
    stream(itemId: $itemId, type: $type, streamId: $streamId) {
      sourceStreamUrl
      size
      type
      method
    }
  }
`

export const GET_PLAY_URL = gql`
  query GetPlayUrl($type: ItemType!, $id: ID!, $ep: String, $lang: Language) {
    play(type: $type, id: $id, ep: $ep, lang: $lang) {
      url
    }
  }
`

// Admin Queries
export const ADMIN_LOGIN = gql`
  mutation AdminLogin($password: String!) {
    adminLogin(password: $password) {
      token
      success
      message
    }
  }
`

export const GET_DATABASE_STATS = gql`
  query GetDatabaseStats {
    databaseStats {
      movies
      series
      anime
      livetv
      totalItems
    }
  }
`

export const GET_CONTENT_OVERVIEW = gql`
  query GetContentOverview {
    contentOverview {
      recentlyAdded
      trending
      mostWatched
      totalViews
    }
  }
`

export const DELETE_ITEM = gql`
  mutation DeleteItem($id: ID!, $type: ItemType!, $adminToken: String!) {
    deleteItem(id: $id, type: $type, adminToken: $adminToken) {
      success
      message
      deletedId
      type
    }
  }
`

export const UPDATE_ITEM = gql`
  mutation UpdateItem($id: ID!, $type: ItemType!, $item: ItemInput!, $adminToken: String!) {
    updateItem(id: $id, type: $type, item: $item, adminToken: $adminToken) {
      success
      message
      item {
        ... on Movie {
          id
          __typename
          title
          displayTitle
          detailUrl
          detailUrlPath
          cleanedTitle
          thumbnail
          image
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          streamingUrls {
            id
            url
            provider
            language
            lastChecked
            isActive
            sourceStreamUrl
            size
            type
            method
          }
          tmdb {
            id
            title
            original_title
            poster_path
            backdrop_path
            overview
            release_date
            vote_average
            vote_count
            genres
          }
        }
        ... on Series {
          id
          __typename
          title
          displayTitle
          detailUrl
          detailUrlPath
          cleanedTitle
          thumbnail
          image
          season
          episodes {
            episodeNumber
            season
            language
            streamingUrls {
              id
              url
              provider
              language
              lastChecked
              isActive
              sourceStreamUrl
              size
              type
              method
            }
          }
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          tmdb {
            id
            title
            original_title
            poster_path
            backdrop_path
            overview
            release_date
            vote_average
            vote_count
            genres
            number_of_seasons
            number_of_episodes
            in_production
            status
          }
        }
        ... on Anime {
          id
          __typename
          title
          displayTitle
          detailUrl
          detailUrlPath
          thumbnail
          image
          season
          animeLanguage
          episodes {
            episodeNumber
            season
            language
            streamingUrls {
              id
              url
              provider
              language
              lastChecked
              isActive
              sourceStreamUrl
              size
              type
              method
            }
          }
          metadata {
            synopsis
            actors
            year
            genre
            origin
            creator
            duration
          }
          jikan {
            mal_id
            title {
              default
              english
              japanese
            }
            type
            source
            episodes
            status
            airing
            aired {
              from
              to
              string
            }
            duration
            rating
            score
            scored_by
            rank
            popularity
            members
            favorites
            synopsis
            background
            season
            year
            studios {
              mal_id
              name
            }
            genres {
              mal_id
              name
              type
            }
            themes {
              mal_id
              name
              type
            }
            demographics {
              mal_id
              name
              type
            }
            streaming_platforms {
              name
              url
            }
            lastUpdated
          }
        }
      }
    }
  }
`

export const GET_ITEM_BY_ID = gql`
  query GetItemById($id: ID!, $type: ItemType!) {
    item(id: $id, type: $type) {
      ... on Movie {
        id
        __typename
        title
        displayTitle
        detailUrl
        detailUrlPath
        cleanedTitle
        thumbnail
        image
        metadata {
          synopsis
          actors
          year
          genre
          origin
          creator
          duration
        }
        streamingUrls {
          id
          url
          provider
          language
          lastChecked
          isActive
          sourceStreamUrl
          size
          type
          method
        }
        tmdb {
          id
          title
          original_title
          poster_path
          backdrop_path
          overview
          release_date
          vote_average
          vote_count
          genres
          cast {
            id
            name
            character
          }
          crew {
            id
            name
            job
          }
        }
      }
      ... on Series {
        id
        __typename
        title
        displayTitle
        detailUrl
        detailUrlPath
        cleanedTitle
        thumbnail
        image
        season
        episodes {
          episodeNumber
          season
          language
          streamingUrls {
            id
            url
            provider
            language
            lastChecked
            isActive
            sourceStreamUrl
            size
            type
            method
          }
        }
        metadata {
          synopsis
          actors
          year
          genre
          origin
          creator
          duration
        }
        tmdb {
          id
          title
          original_title
          poster_path
          backdrop_path
          overview
          release_date
          vote_average
          vote_count
          genres
          cast {
            id
            name
            character
          }
          crew {
            id
            name
            job
          }
          number_of_seasons
          number_of_episodes
          in_production
          status
        }
      }
      ... on Anime {
        id
        __typename
        title
        displayTitle
        detailUrl
        detailUrlPath
        image
        thumbnail
        season
        animeLanguage
        episodes {
          episodeNumber
          season
          language
          streamingUrls {
            id
            url
            provider
            language
            lastChecked
            isActive
            sourceStreamUrl
            size
            type
            method
          }
        }
        metadata {
          synopsis
          actors
          year
          genre
          origin
          creator
          duration
        }
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          type
          source
          episodes
          status
          airing
          aired {
            from
            to
            string
          }
          duration
          rating
          score
          scored_by
          rank
          popularity
          members
          favorites
          synopsis
          background
          season
          year
          studios {
            mal_id
            name
          }
          genres {
            mal_id
            name
            type
          }
          themes {
            mal_id
            name
            type
          }
          demographics {
            mal_id
            name
            type
          }
          images {
            jpg {
              image_url
              small_image_url
              large_image_url
            }
            webp {
              image_url
              small_image_url
              large_image_url
            }
          }
          trailer {
            youtube_id
            url
            embed_url
          }
          approved
          relations {
            relation
            entry {
              mal_id
              type
              name
              url
            }
          }
          streaming_platforms {
            name
            url
          }
          lastUpdated
        }
      }
    }
  }
`

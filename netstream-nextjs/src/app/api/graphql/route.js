import { NextResponse } from 'next/server'

const GRAPHQL_ENDPOINT = (process.env.NEXT_PUBLIC_API_BASE_URL ? process.env.NEXT_PUBLIC_API_BASE_URL : 'http://localhost:3001') + '/graphql'

export async function POST(request) {
  try {
    const body = await request.json()
    
    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()
    
    return NextResponse.json(data)
  } catch (error) {
    console.error('GraphQL API route error:', error)
    return NextResponse.json(
      { errors: [{ message: 'Internal server error' }] },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'GraphQL endpoint - use POST method' },
    { status: 405 }
  )
}

'use client';

import { useState, useEffect } from 'react';
import styles from './admin.module.css';

export default function ConfigurationTab() {
  const [configData, setConfigData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingKey, setEditingKey] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchConfigData();
  }, []);

  const fetchConfigData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/config');
      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();
      if (result.success) {
        setConfigData(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (key, currentValue) => {
    setEditingKey(key);
    setEditValue(currentValue);
  };

  const handleSave = async () => {
    if (!editingKey || editValue.trim() === '') return;

    try {
      setIsSaving(true);
      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          key: editingKey,
          value: editValue.trim()
        })
      });

      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();
      
      if (result.success) {
        // Update local state
        setConfigData(prev => ({
          ...prev,
          baseUrls: {
            ...prev.baseUrls,
            [editingKey.replace('_BASE', '').toLowerCase()]: editValue.trim()
          }
        }));
        setEditingKey(null);
        setEditValue('');
        alert('Configuration updated successfully!');
      } else {
        throw new Error(result.error || 'Failed to update configuration');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingKey(null);
    setEditValue('');
  };

  const toggleSystemSetting = async (setting) => {
    try {
      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          key: setting,
          value: !configData.systemSettings[setting]
        })
      });

      if (!response.ok) throw new Error(`Config API returned ${response.status}`);
      const result = await response.json();
      
      if (result.success) {
        setConfigData(prev => ({
          ...prev,
          systemSettings: {
            ...prev.systemSettings,
            [setting]: !prev.systemSettings[setting]
          }
        }));
        alert(`${setting} ${!configData.systemSettings[setting] ? 'enabled' : 'disabled'} successfully!`);
      } else {
        throw new Error(result.error || 'Failed to update setting');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div>
      <div className={styles['system-controls']}>
        <button className="button primary" onClick={fetchConfigData}>
          <i className="fas fa-sync"></i> Refresh Data
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Base URLs Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-link text-blue-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Base URLs</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.baseUrls ? (
            <div className="space-y-4">
              {Object.entries(configData.baseUrls).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white capitalize">{key}</div>
                    {editingKey === key ? (
                      <div className="flex items-center mt-1">
                        <input
                          type="text"
                          value={editValue}
                          onChange={(e) => setEditValue(e.target.value)}
                          className="flex-1 bg-gray-700 text-white px-3 py-1 rounded border border-gray-600 text-sm"
                          placeholder="Enter new value"
                        />
                        <button
                          onClick={handleSave}
                          disabled={isSaving}
                          className="ml-2 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
                        >
                          {isSaving ? 'Saving...' : 'Save'}
                        </button>
                        <button
                          onClick={handleCancel}
                          className="ml-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                        >
                          Cancel
                        </button>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-400 mt-1">{value}</div>
                    )}
                  </div>
                  {editingKey !== key && (
                    <button
                      onClick={() => handleEdit(key, value)}
                      className="ml-4 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      Edit
                    </button>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No base URLs configuration available</div>
          )}
        </div>

        {/* API Keys Configuration */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-key text-green-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">API Keys</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.apiKeys ? (
            <div className="space-y-4">
              {Object.entries(configData.apiKeys).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white">{key.toUpperCase()}</div>
                    <div className="text-sm text-gray-400 mt-1">{value}</div>
                  </div>
                  <button
                    onClick={() => alert('API key management would be implemented here')}
                    className="ml-4 px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                  >
                    Manage
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No API keys configuration available</div>
          )}
        </div>

        {/* System Settings */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-cog text-purple-500 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">System Settings</h3>
          </div>
          {isLoading ? (
            <div className={styles.loading}><i className="fas fa-spinner"></i> Loading...</div>
          ) : error ? (
            <div className="text-red-500 text-sm">{error}</div>
          ) : configData?.systemSettings ? (
            <div className="space-y-4">
              {Object.entries(configData.systemSettings).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-white">
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </div>
                    <div className="text-sm text-gray-400 mt-1">
                      {typeof value === 'boolean' ? (value ? 'Enabled' : 'Disabled') : value}
                    </div>
                  </div>
                  {typeof value === 'boolean' ? (
                    <button
                      onClick={() => toggleSystemSetting(key)}
                      className={`ml-4 px-3 py-1 rounded text-sm ${
                        value 
                          ? 'bg-red-600 text-white hover:bg-red-700' 
                          : 'bg-green-600 text-white hover:bg-green-700'
                      }`}
                    >
                      {value ? 'Disable' : 'Enable'}
                    </button>
                  ) : (
                    <div className="ml-4 text-sm text-gray-400">Read-only</div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No system settings available</div>
          )}
        </div>

        {/* Configuration Info */}
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <i className="fas fa-info-circle text-blue-400 text-2xl mr-4"></i>
            <h3 className="text-lg font-semibold text-white">Configuration Info</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Environment</span>
              <span className="text-white font-semibold">{process.env.NODE_ENV || 'development'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Last Updated</span>
              <span className="text-white font-semibold">
                {configData?.timestamp ? new Date(configData.timestamp).toLocaleString() : 'Never'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Configuration Version</span>
              <span className="text-white font-semibold">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Auto Backup</span>
              <span className={`font-semibold ${configData?.systemSettings?.autoBackup ? 'text-green-500' : 'text-red-500'}`}>
                {configData?.systemSettings?.autoBackup ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration Actions */}
      <div className="bg-gray-800 rounded-lg p-6 mt-6">
        <div className="flex items-center mb-4">
          <i className="fas fa-tools text-yellow-500 text-2xl mr-4"></i>
          <h3 className="text-lg font-semibold text-white">Configuration Actions</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => alert('Export configuration functionality would be implemented here')}
            className="button secondary"
          >
            <i className="fas fa-download"></i> Export Config
          </button>
          <button
            onClick={() => alert('Import configuration functionality would be implemented here')}
            className="button secondary"
          >
            <i className="fas fa-upload"></i> Import Config
          </button>
          <button
            onClick={() => {
              if (confirm('Are you sure you want to reset all configuration to defaults?')) {
                alert('Reset configuration functionality would be implemented here')
              }
            }}
            className="button danger"
          >
            <i className="fas fa-undo"></i> Reset to Defaults
          </button>
        </div>
      </div>
    </div>
  );
}

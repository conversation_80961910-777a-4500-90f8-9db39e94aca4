/**
 * Gemini AI Integration for Addic7ed Show Matching
 *
 * This module uses the Gemini AI API to find the best match between a show title
 * from our database and the available shows on Addic7ed.
 */

const fetch = require('node-fetch');
const logger = require('./logger');
const { getConfig } = require('../enrichment/config/enrichmentConfig');

// Get rate limit from config
const config = getConfig();
const GEMINI_RATE_LIMIT_MS = config.GEMINI_RATE_LIMIT_MS || 2000; // Default: 30 req/min = 2000ms

// Gemini API configuration
const GEMINI_MODEL_NAME = "gemini-2.0-flash-lite";
let GEMINI_API_KEY = null;
let GEMINI_API_URL = null;

// Function to get Gemini API key from database or environment
async function getGeminiApiKey() {
  try {
    // Try to get from database first
    const { MongoClient } = require('mongodb');
    const uri = process.env.MONGO_URI;
    if (!uri) {
      logger.warn('Gemini AI: MONGO_URI not found in environment variables');
      return process.env.GEMINI_API_KEY;
    }
    const client = new MongoClient(uri);
    await client.connect();
    const db = client.db('netstream');
    const config = await db.collection('config').findOne({ key: 'GEMINI_API_KEY' });
    await client.close();

    if (config && config.value) {
      return config.value;
    }
  } catch (error) {
    logger.warn(`Gemini AI: Failed to get API key from database: ${error.message}`);
  }

  // Fallback to environment variable
  return process.env.GEMINI_API_KEY;
}

// Function to initialize API key and URL
async function initializeGeminiConfig() {
  GEMINI_API_KEY = await getGeminiApiKey();
  GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL_NAME}:generateContent?key=${GEMINI_API_KEY}`;
  return GEMINI_API_KEY;
}

// Rate limiting variables
let lastGeminiCallTime = 0;

// Sleep function for rate limiting
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Find the best match for a show title in the Addic7ed show list using Gemini AI
 * @param {string} showTitle - The show title from our database
 * @param {Array<string|Object>} addic7edShows - List of shows available on Addic7ed
 * @returns {Promise<Object|null>} The best matching show object with id and name, or null if no good match
 */
async function findBestShowMatch(showTitle, addic7edShows) {
  try {
    logger.info(`Finding best match for "${showTitle}" using Gemini AI`);

    // Initialize Gemini configuration with latest API key
    const apiKey = await initializeGeminiConfig();
    if (!apiKey) {
      logger.warn('Gemini AI: No API key available, cannot perform show matching');
      return null;
    }

    // Convert shows to array of objects with id and name if they're not already
    const showsWithIds = Array.isArray(addic7edShows) && addic7edShows.length > 0 &&
                         typeof addic7edShows[0] === 'object' && addic7edShows[0].id ?
                         addic7edShows : await getAddic7edShowList(true);

    // Extract just the names for simpler matching
    const showNames = showsWithIds.map(show => show.name);

    // Handle special cases directly
    if (showTitle.toLowerCase().replace(/[^a-z0-9]/g, '') === 'lovedeathrobots') {
      const specialMatchIndex = showNames.findIndex(name =>
        name.toLowerCase().includes('love') &&
        name.toLowerCase().includes('death') &&
        name.toLowerCase().includes('robots')
      );

      if (specialMatchIndex !== -1) {
        const match = showsWithIds[specialMatchIndex];
        logger.info(`Found special case match: "${match.name}" (ID: ${match.id}) for "${showTitle}"`);
        return match;
      }
    }

    // Normalize titles by removing special characters for comparison
    const normalizeTitle = (title) => {
      return title
        .toLowerCase()
        .replace(/[&,:']/g, ' ')  // Replace special chars with spaces
        .replace(/\s+/g, ' ')     // Replace multiple spaces with single space
        .trim();
    };

    // Simple exact match check first (case insensitive)
    const exactMatchIndex = showNames.findIndex(name =>
      name.toLowerCase() === showTitle.toLowerCase()
    );

    if (exactMatchIndex !== -1) {
      const match = showsWithIds[exactMatchIndex];
      logger.info(`Found exact match (case insensitive): "${match.name}" (ID: ${match.id})`);
      return match;
    }

    // Try normalized matching
    const normalizedShowTitle = normalizeTitle(showTitle);
    const normalizedMatchIndex = showNames.findIndex(name =>
      normalizeTitle(name) === normalizedShowTitle
    );

    if (normalizedMatchIndex !== -1) {
      const match = showsWithIds[normalizedMatchIndex];
      logger.info(`Found normalized match: "${match.name}" (ID: ${match.id}) for "${showTitle}"`);
      return match;
    }

    // Simple check for franchise shows
    // For example, if showTitle is "Star Wars: Andor" and addic7edShows contains "Andor"
    const simplifiedTitle = showTitle
      .replace(/^(Star Wars|Marvel|DC|Disney|HBO|Netflix|Amazon|Hulu|BBC)[\s:]+/i, '')
      .trim();

    if (simplifiedTitle !== showTitle) {
      const simplifiedMatchIndex = showNames.findIndex(name =>
        name.toLowerCase() === simplifiedTitle.toLowerCase()
      );

      if (simplifiedMatchIndex !== -1) {
        const match = showsWithIds[simplifiedMatchIndex];
        logger.info(`Found match after removing franchise prefix: "${match.name}" (ID: ${match.id})`);
        return match;
      }

      // Try normalized matching with simplified title
      const normalizedSimplifiedTitle = normalizeTitle(simplifiedTitle);
      const normalizedSimplifiedMatchIndex = showNames.findIndex(name =>
        normalizeTitle(name) === normalizedSimplifiedTitle
      );

      if (normalizedSimplifiedMatchIndex !== -1) {
        const match = showsWithIds[normalizedSimplifiedMatchIndex];
        logger.info(`Found normalized match after removing franchise prefix: "${match.name}" (ID: ${match.id})`);
        return match;
      }
    }

    // Try fuzzy matching for titles with special characters
    const fuzzyMatchIndices = showNames.reduce((matches, name, index) => {
      const normalizedShow = normalizeTitle(name);
      const normalizedInput = normalizedShowTitle;

      // Check if all words in the input title are in the show title
      const inputWords = normalizedInput.split(' ').filter(word => word.length > 1);
      if (inputWords.every(word => normalizedShow.includes(word))) {
        matches.push(index);
      }
      return matches;
    }, []);

    if (fuzzyMatchIndices.length === 1) {
      const match = showsWithIds[fuzzyMatchIndices[0]];
      logger.info(`Found fuzzy match: "${match.name}" (ID: ${match.id}) for "${showTitle}"`);
      return match;
    } else if (fuzzyMatchIndices.length > 1) {
      // If multiple matches, find the closest one by length
      fuzzyMatchIndices.sort((a, b) => {
        const aDiff = Math.abs(normalizeTitle(showNames[a]).length - normalizedShowTitle.length);
        const bDiff = Math.abs(normalizeTitle(showNames[b]).length - normalizedShowTitle.length);
        return aDiff - bDiff;
      });

      const match = showsWithIds[fuzzyMatchIndices[0]];
      logger.info(`Found closest fuzzy match: "${match.name}" (ID: ${match.id}) for "${showTitle}" from ${fuzzyMatchIndices.length} candidates`);
      return match;
    }

    // Limit the number of shows to avoid exceeding token limits
    const limitedShowList = showNames.slice(0, 200);

    // Create the prompt for Gemini
    const prompt = `
I need to find the best match for a TV show title in a list of available shows.

My show title: "${showTitle}"

Available shows on the subtitle website:
${limitedShowList.join('\n')}

Please find the EXACT match from the available shows list that best corresponds to my show title.
Consider variations like:
- Franchise prefixes (e.g., "Star Wars: Andor" vs "Andor")
- "The" at the beginning
- Special characters and spacing (e.g., "Love Death Robots" vs "Love, Death & Robots")
- Season information
- Ampersands and other special characters

Return ONLY the exact matching title from the list with no additional text or explanation.
If there is no good match, return "NO_MATCH".
`;

    // Apply rate limiting
    const now = Date.now();
    const elapsed = now - lastGeminiCallTime;
    if (lastGeminiCallTime > 0 && elapsed < GEMINI_RATE_LIMIT_MS) {
      const wait = GEMINI_RATE_LIMIT_MS - elapsed;
      logger.debug(`Gemini API rate limit: Waiting ${wait}ms before API call...`);
      await sleep(wait);
    }

    // Call the Gemini API
    lastGeminiCallTime = Date.now();
    const response = await fetch(GEMINI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const data = await response.json();

    // Extract the response text
    const responseText = data.candidates[0]?.content?.parts[0]?.text?.trim();

    if (!responseText || responseText === 'NO_MATCH') {
      logger.warn(`No good match found for "${showTitle}"`);
      return null;
    }

    // Find the match in the show list
    const matchIndex = showNames.findIndex(name => name === responseText);
    if (matchIndex === -1) {
      logger.warn(`Gemini returned "${responseText}" but it's not in the Addic7ed show list`);

      // Try to find a close match in the list
      const closestMatchIndex = showNames.findIndex(name =>
        name.toLowerCase() === responseText.toLowerCase()
      );

      if (closestMatchIndex !== -1) {
        const match = showsWithIds[closestMatchIndex];
        logger.info(`Found close match: "${match.name}" (ID: ${match.id})`);
        return match;
      }

      return null;
    }

    const match = showsWithIds[matchIndex];
    logger.info(`Found match: "${match.name}" (ID: ${match.id}) for "${showTitle}"`);
    return match;
  } catch (error) {
    logger.error(`Error using Gemini AI for show matching: ${error.message}`);

    // Handle rate limiting or other API errors gracefully
    if (error.message.includes('429') || error.message.includes('rate limit')) {
      logger.warn('Gemini API rate limit reached, falling back to basic matching');
    }

    // Try a simple fallback for common franchise shows and special cases
    try {
      const showsWithIds = await getAddic7edShowList(true);

      // Handle common franchise shows
      if (showTitle.toLowerCase().includes('star wars') && showTitle.toLowerCase().includes('andor')) {
        const match = showsWithIds.find(show => show.name === 'Andor');
        if (match) return match;
      }
      if (showTitle.toLowerCase().includes('star wars') && showTitle.toLowerCase().includes('mandalorian')) {
        const match = showsWithIds.find(show => show.name === 'The Mandalorian');
        if (match) return match;
      }
      if (showTitle.toLowerCase().includes('star wars') && showTitle.toLowerCase().includes('ahsoka')) {
        const match = showsWithIds.find(show => show.name === 'Ahsoka');
        if (match) return match;
      }
      if (showTitle.toLowerCase().includes('last of us')) {
        const match = showsWithIds.find(show => show.name === 'The Last of Us');
        if (match) return match;
      }

      // Handle special cases with punctuation
      if (showTitle.toLowerCase().replace(/[^a-z0-9]/g, '') === 'lovedeathrobots') {
        const match = showsWithIds.find(show => show.name === 'Love, Death & Robots');
        if (match) return match;
      }
    } catch (fallbackError) {
      logger.error(`Error in fallback matching: ${fallbackError.message}`);
    }

    return null;
  }
}

/**
 * Fetch the list of shows available on Addic7ed
 * @param {boolean} includeIds - Whether to include show IDs in the result
 * @returns {Promise<Array<string|Object>>} List of show titles or objects with id and name
 */
async function fetchAddic7edShowList(includeIds = false) {
  try {
    logger.info('Fetching Addic7ed show list');

    // Use a hardcoded list of common shows as a fallback
    // This ensures we always have some shows to match against even if the fetch fails
    const fallbackShows = [
      { id: "7252", name: "Andor" },
      { id: "7239", name: "The Last of Us" },
      { id: "7023", name: "House of the Dragon" },
      { id: "6857", name: "The Mandalorian" },
      { id: "9407", name: "Ahsoka" },
      { id: "6857", name: "Loki" },
      { id: "164", name: "Game of Thrones" },
      { id: "185", name: "Breaking Bad" },
      { id: "4477", name: "Better Call Saul" },
      { id: "5559", name: "Stranger Things" },
      { id: "6928", name: "The Boys" },
      { id: "6844", name: "The Witcher" },
      { id: "79", name: "The Office" },
      { id: "110", name: "Friends" },
      { id: "126", name: "The Big Bang Theory" },
      { id: "2710", name: "Rick and Morty" },
      { id: "121", name: "South Park" },
      { id: "84", name: "Family Guy" },
      { id: "82", name: "The Simpsons" },
      { id: "3322", name: "Brooklyn Nine-Nine" },
      { id: "1542", name: "Parks and Recreation" },
      { id: "6312", name: "The Good Place" },
      { id: "5852", name: "The Crown" },
      { id: "2718", name: "Peaky Blinders" },
      { id: "4773", name: "Narcos" },
      { id: "6762", name: "Money Heist" },
      { id: "6616", name: "Dark" },
      { id: "6297", name: "Mindhunter" },
      { id: "4378", name: "True Detective" },
      { id: "4587", name: "Fargo" },
      { id: "5559", name: "Westworld" },
      { id: "2691", name: "Black Mirror" },
      { id: "6297", name: "The Handmaid's Tale" },
      { id: "7407", name: "Chernobyl" },
      { id: "1520", name: "Band of Brothers" },
      { id: "1521", name: "The Pacific" },
      { id: "1402", name: "The Walking Dead" },
      { id: "5270", name: "Fear the Walking Dead" },
      { id: "7185", name: "The Umbrella Academy" },
      { id: "7030", name: "The Haunting of Hill House" },
      { id: "8207", name: "The Haunting of Bly Manor" },
      { id: "1425", name: "American Horror Story" },
      { id: "95", name: "Supernatural" },
      { id: "69", name: "The X-Files" },
      { id: "705", name: "Twin Peaks" },
      { id: "108", name: "Lost" },
      { id: "7", name: "24" },
      { id: "167", name: "Prison Break" },
      { id: "161", name: "Dexter" },
      { id: "3182", name: "Hannibal" },
      { id: "6297", name: "Mindhunter" },
      { id: "5559", name: "Ozark" },
      { id: "4773", name: "Narcos" },
      { id: "7134", name: "Narcos: Mexico" },
      { id: "8207", name: "The Queen's Gambit" }
    ];

    try {
      // First try to load from the local JSON file
      const fs = require('fs');
      const path = require('path');
      const showsFilePath = path.join(__dirname, '..', 'data', 'addic7ed_shows.json');

      if (fs.existsSync(showsFilePath)) {
        try {
          const showsData = fs.readFileSync(showsFilePath, 'utf8');
          const shows = JSON.parse(showsData);

          if (Array.isArray(shows) && shows.length > 0) {
            logger.info(`Loaded ${shows.length} shows from local JSON file`);

            // Return either the full objects or just the names
            if (includeIds) {
              return shows;
            } else {
              return shows.map(show => show.name);
            }
          } else {
            logger.warn('Local JSON file contains no shows, falling back to online fetch');
          }
        } catch (fileError) {
          logger.warn(`Error reading local JSON file: ${fileError.message}, falling back to online fetch`);
        }
      } else {
        logger.info('Local JSON file not found, falling back to online fetch');
      }

      // If local file doesn't exist or is invalid, try to fetch from Addic7ed
      const headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9'
      };

      // Fetch the shows page
      const response = await fetch('https://www.addic7ed.com/shows.php', {
        headers,
        timeout: 10000
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const html = await response.text();

      // Extract show titles using regex
      // The shows are in <option value="123">Show Title</option> format
      const showRegex = /<option[^>]*value="(\d+)"[^>]*>([^<]+)<\/option>/g;
      const shows = [];
      let match;

      while ((match = showRegex.exec(html)) !== null) {
        const id = match[1];
        const name = match[2].trim();

        if (includeIds) {
          shows.push({ id, name });
        } else {
          shows.push(name);
        }
      }

      if (shows.length > 0) {
        logger.info(`Found ${shows.length} shows on Addic7ed`);
        return shows;
      } else {
        logger.warn('No shows found in Addic7ed response, using fallback list');
        return includeIds ? fallbackShows : fallbackShows.map(show => show.name);
      }
    } catch (error) {
      logger.warn(`Error fetching Addic7ed show list: ${error.message}, using fallback list`);
      return includeIds ? fallbackShows : fallbackShows.map(show => show.name);
    }
  } catch (error) {
    logger.error(`Error in fetchAddic7edShowList: ${error.message}`);
    return includeIds ? fallbackShows : fallbackShows.map(show => show.name);
  }
}

// Cache for Addic7ed show list to avoid fetching it repeatedly
let addic7edShowListCache = null;
let addic7edShowListWithIdsCache = null;
let lastFetchTime = 0;
const CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Get the Addic7ed show list (from cache if available)
 * @param {boolean} includeIds - Whether to include show IDs in the result
 * @returns {Promise<Array<string|Object>>} List of show titles or objects with id and name
 */
async function getAddic7edShowList(includeIds = false) {
  const now = Date.now();

  // Use cached list if available and not expired
  if (includeIds) {
    if (addic7edShowListWithIdsCache && (now - lastFetchTime < CACHE_TTL)) {
      logger.info(`Using cached Addic7ed show list with IDs (${addic7edShowListWithIdsCache.length} shows)`);
      return addic7edShowListWithIdsCache;
    }
  } else {
    if (addic7edShowListCache && (now - lastFetchTime < CACHE_TTL)) {
      logger.info(`Using cached Addic7ed show list (${addic7edShowListCache.length} shows)`);
      return addic7edShowListCache;
    }
  }

  // Fetch fresh list
  const shows = await fetchAddic7edShowList(includeIds);

  // Update cache
  if (shows.length > 0) {
    if (includeIds) {
      addic7edShowListWithIdsCache = shows;
    } else {
      addic7edShowListCache = shows;
    }
    lastFetchTime = now;
  }

  return shows;
}

module.exports = {
  findBestShowMatch,
  getAddic7edShowList
};

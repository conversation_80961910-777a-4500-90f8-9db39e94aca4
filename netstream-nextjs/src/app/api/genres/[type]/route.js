import { NextResponse } from 'next/server'

// Extract the base URL from NEXT_PUBLIC_API_URL or use default
const GRAPHQL_ENDPOINT = process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql'

const GENRE_QUERIES = {
  movies: `
    query GetMoviesByGenre($genre: String!, $limit: Int, $page: Int) {
      moviesByGenre(genre: $genre, limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  series: `
    query GetSeriesByGenre($genre: String!, $limit: Int, $page: Int) {
      seriesByGenre(genre: $genre, limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        season
        episodes {
          episodeNumber
          streamingUrls {
            language
          }
        }
        metadata {
          year
          synopsis
        }
        tmdb {
          id
          title
          overview
          poster_path
          release_date
          vote_average
          vote_count
          genres
        }
      }
    }
  `,
  anime: `
    query GetAnimeByGenre($genre: String!, $limit: Int, $page: Int) {
      animeByGenre(genre: $genre, limit: $limit, page: $page) {
        id
        title
        displayTitle
        thumbnail
        image
        animeLanguage
        season
        episodes {
          episodeNumber
        }
        metadata {
          year
          synopsis
        }
        jikan {
          mal_id
          title {
            default
            english
            japanese
          }
          images {
            jpg {
              image_url
              large_image_url
            }
          }
          score
          aired {
            from
          }
          genres {
            name
          }
        }
      }
    }
  `
}

export async function GET(request, { params }) {
  let type
  try {
    const resolvedParams = await params
    type = resolvedParams.type
    const { searchParams } = new URL(request.url)
    const genre = searchParams.get('genre')
    
    console.log(`🔄 Genre API: Fetching ${genre} ${type}...`)

    if (!GENRE_QUERIES[type]) {
      return NextResponse.json(
        { success: false, error: `Invalid type: ${type}` },
        { status: 400 }
      )
    }

    if (!genre) {
      return NextResponse.json(
        { success: false, error: 'Genre parameter is required' },
        { status: 400 }
      )
    }

    const variables = {
      genre,
      limit: 20,
      page: 1
    }

    const response = await fetch(GRAPHQL_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: GENRE_QUERIES[type],
        variables
      })
    })

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.status}`)
    }

    const result = await response.json()
    console.log(`📦 Genre API: GraphQL result for ${genre} ${type}:`, result)

    if (result.errors) {
      console.error(`❌ Genre API: GraphQL errors for ${genre} ${type}:`, result.errors)
      return NextResponse.json(
        { success: false, error: result.errors[0]?.message || 'GraphQL error' },
        { status: 500 }
      )
    }

    const queryName = `${type}ByGenre`
    const items = result.data?.[queryName] || []
    
    console.log(`✅ Genre API: Returning ${items.length} ${genre} ${type} items`)

    return NextResponse.json({
      success: true,
      data: items
    })

  } catch (error) {
    console.error(`💥 Genre API error for ${type || 'unknown'}:`, error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
